/* eslint-disable max-classes-per-file */
import { Readable, Writable } from "node:stream";
import isEqual from "lodash.isequal";
import shuffle from "lodash.shuffle";

import EventBus from "../domain/EventBus.js";

export default class PullTmdbContentJob {
  constructor(
    private eventBus: EventBus<Event>,
    private genreRepository: GenreRepository,
    private imageStorage: ImageStorage,
    private keywordRepository: KeywordRepository,
    private movieRepository: MovieRepository,
    private personRepository: PersonRepository,
    private tmdbGateway: TmdbGateway,
    private queue: JobQueue,
    private debug: (message: string) => void,
  ) {}

  async run(): Promise<void> {
    await this.pullNewMovies();
    await this.pullNewPeople();
    await this.pullRandomMovies();
    await this.pullRandomPeople();
    await this.downloadImages();
    await this.deleteBrokenImages();
  }

  private async pullNewMovies(): Promise<void> {
    const tmdbIds = await this.queue.getMovieJobQueue();
    const movies = await this.movieRepository.findMany(tmdbIds);
    const createdMovies = tmdbIds
      .map((tmdbId, index) => {
        const movie = movies[index];

        return movie ? null : createMovie(this.eventBus, { id: tmdbId });
      })
      .filter((x): x is Movie => x !== null);

    if (createdMovies.length > 0) {
      this.debug(`Pulling ${createdMovies.length} new movies`);
    }

    for (let movie of createdMovies) {
      movie = await this.pullMovie(movie);
      await this.movieRepository.setMany([movie]);
    }
  }

  private async pullRandomMovies(): Promise<void> {
    let movies = await this.movieRepository.findAll();

    movies = shuffle(movies).slice(0, 200);

    this.debug(`Pulling ${movies.length} random existing movies`);

    for (let movie of movies) {
      movie = await this.pullMovie(movie);
      await this.movieRepository.setMany([movie]);
    }
  }

  private async pullNewPeople(): Promise<void> {
    const tmdbIds = await this.queue.getPeopleJobQueue();
    const people = await this.personRepository.findMany(tmdbIds);
    const createdPeople = tmdbIds
      .map((tmdbId, index) => {
        const person = people[index];

        return person ? null : createPerson(this.eventBus, { id: tmdbId });
      })
      .filter((x): x is Person => x !== null);

    if (createdPeople.length > 0) {
      this.debug(`Pulling ${createdPeople.length} new people`);
    }

    for (let person of createdPeople) {
      person = await this.pullPerson(person);
      await this.personRepository.setMany([person]);
    }
  }

  private async pullRandomPeople(): Promise<void> {
    let people = await this.personRepository.findAll();

    people = shuffle(people).slice(0, 200);

    this.debug(`Pulling ${people.length} random existing people`);

    for (let person of people) {
      person = await this.pullPerson(person);
      await this.personRepository.setMany([person]);
    }
  }

  private async downloadImages(): Promise<void> {
    const movies = await this.movieRepository.findAll();
    const people = await this.personRepository.findAll();
    const images = [
      ...movies
        .filter((movie) => movie.images.length > 0)
        .map(
          (movie) =>
            [...movie.images[0].sizes]
              .sort(
                (a, b) => Math.abs(a.width - 1280) - Math.abs(b.width - 1280),
              )
              .slice(0, 1)[0],
        ),
      ...people
        .filter((person) => person.images.length > 0)
        .map(
          (movie) =>
            [...movie.images[0].sizes]
              .sort((a, b) => Math.abs(a.width - 180) - Math.abs(b.width - 180))
              .slice(0, 1)[0],
        ),
    ];
    const existingImages = await this.imageStorage.findMany(
      images.map((image) => image.url),
    );
    const nonDownloadedImages = images.filter(
      (image, index) => existingImages[index] === null,
    );

    if (nonDownloadedImages.length > 0) {
      this.debug(`Downloading ${nonDownloadedImages.length} new images`);
    }

    await this.tmdbGateway.downloadAsManyImagesAsPossible(
      nonDownloadedImages.map((image) => ({
        height: image.height,
        width: image.width,
        sourceUrl: image.url,
      })),
      async (stream, input) => {
        const image = await this.imageStorage.upload(stream, input);

        this.eventBus.push({
          payload: {
            sourceUrl: image.sourceUrl,
            url: image.url,
          },
          type: "ImageDownloadedEvent",
        });
      },
    );
  }

  private async deleteBrokenImages(): Promise<void> {
    const broken = await this.imageStorage.findBroken();

    if (broken.length > 0) {
      this.debug(`Deleteing ${broken.length} broken images`);
    }

    await this.imageStorage.deleteMany(broken);

    for (const image of broken) {
      this.eventBus.push({
        payload: {
          sourceUrl: image.sourceUrl,
          url: image.url,
        },
        type: "ImageDeletedEvent",
      });
    }
  }

  private async pullMovie(movie: Movie): Promise<Movie> {
    const response = await this.tmdbGateway.fetchMovie(movie.id);

    if (response) {
      const existingGenres = await this.genreRepository.findMany(
        response.genres.map((genre) => genre.id),
      );
      const createdGenres = response.genres
        .map((genre, index): Genre | null => {
          return existingGenres[index]
            ? null
            : createGenre(this.eventBus, genre);
        })
        .filter((x): x is Genre => x !== null);

      await this.genreRepository.setMany(createdGenres);

      const existingKeywords = await this.keywordRepository.findMany(
        response.keywords.map((keyword) => keyword.id),
      );
      const createdKeywords = response.keywords
        .map((keyword, index): Keyword | null => {
          return existingKeywords[index]
            ? null
            : createKeyword(this.eventBus, keyword);
        })
        .filter((x): x is Keyword => x !== null);

      await this.keywordRepository.setMany(createdKeywords);

      const updatedMovie = updateMovie(this.eventBus, movie, {
        id: movie.id,
        images: response.images,
        genreIds: response.genres.map((genre) => genre.id),
        keywordIds: response.keywords.map((keyword) => keyword.id),
      });

      const imagesToDelete = (
        await this.imageStorage.findMany(
          getMovieDeletedImagesUrls(movie, updatedMovie),
        )
      ).filter((image): image is StorageImage => image !== null);

      if (imagesToDelete.length > 0) {
        this.debug(`Removing ${imagesToDelete.length} obsolete images`);
      }

      await this.imageStorage.deleteMany(imagesToDelete);

      return updatedMovie;
    }

    return movie;
  }

  private async pullPerson(person: Person): Promise<Person> {
    const response = await this.tmdbGateway.fetchPerson(person.id);

    if (response) {
      const updatedPerson = updatePerson(this.eventBus, person, {
        id: person.id,
        images: response.images,
      });

      const imagesToDelete = (
        await this.imageStorage.findMany(
          getPersonDeletedImagesUrls(person, updatedPerson),
        )
      ).filter((image): image is StorageImage => image !== null);

      if (imagesToDelete.length > 0) {
        this.debug(`Removing ${imagesToDelete.length} obsolete images`);
      }

      await this.imageStorage.deleteMany(imagesToDelete);

      return updatedPerson;
    }

    return person;
  }
}

function getMovieDeletedImagesUrls(
  prevMovie: Movie,
  nextMovie: Movie,
): string[] {
  const prevImages = prevMovie.images.flatMap((image) => image.sizes);
  const nextImages = nextMovie.images.flatMap((image) => image.sizes);

  const { deleted } = setDiff(
    new Set(prevImages),
    new Set(nextImages),
    (image) => image.url,
  );

  return [...deleted].map((image) => image.url);
}

function getPersonDeletedImagesUrls(
  prevPerson: Person,
  nextPerson: Person,
): string[] {
  const prevImages = prevPerson.images.flatMap((image) => image.sizes);
  const nextImages = nextPerson.images.flatMap((image) => image.sizes);

  const { deleted } = setDiff(
    new Set(prevImages),
    new Set(nextImages),
    (image) => image.url,
  );

  return [...deleted].map((image) => image.url);
}

function setDiff<T, I>(
  prev: Set<T>,
  next: Set<T>,
  identity: (t: T) => I,
): {
  created: Set<T>;
  deleted: Set<T>;
} {
  const created = new Set<T>();
  const deleted = new Set<T>();
  const prevIndex = new Map<I, T>();
  const nextIndex = new Map<I, T>();

  prev.forEach((x) => {
    prevIndex.set(identity(x), x);
  });

  next.forEach((x) => {
    nextIndex.set(identity(x), x);
  });

  next.forEach((x) => {
    if (!prevIndex.has(identity(x))) {
      created.add(x);
    }
  });

  prev.forEach((x) => {
    if (!nextIndex.has(identity(x))) {
      deleted.add(x);
    }
  });

  return { created, deleted };
}

export interface MovieCreatedEvent {
  payload: {
    id: string;
  };
  type: "MovieCreatedEvent";
}

export interface MovieImagesUpdatedEvent {
  payload: {
    id: string;
  };
  type: "MovieImagesUpdatedEvent";
}

export interface MovieGenresUpdatedEvent {
  payload: {
    id: string;
  };
  type: "MovieGenresUpdatedEvent";
}

export interface MovieKeywordsUpdatedEvent {
  payload: {
    id: string;
  };
  type: "MovieKeywordsUpdatedEvent";
}

export interface PersonCreatedEvent {
  payload: {
    id: string;
  };
  type: "PersonCreatedEvent";
}

export interface PersonImagesUpdatedEvent {
  payload: {
    id: string;
  };
  type: "PersonImagesUpdatedEvent";
}

export interface ImageDownloadedEvent {
  payload: {
    sourceUrl: string;
    url: string;
  };
  type: "ImageDownloadedEvent";
}

export interface ImageDeletedEvent {
  payload: {
    sourceUrl: string;
    url: string;
  };
  type: "ImageDeletedEvent";
}

export interface GenreCreatedEvent {
  payload: {
    id: string;
    name: string;
  };
  type: "GenreCreatedEvent";
}

export interface KeywordCreatedEvent {
  payload: {
    id: string;
    name: string;
  };
  type: "KeywordCreatedEvent";
}

export type Event =
  | MovieCreatedEvent
  | MovieGenresUpdatedEvent
  | MovieImagesUpdatedEvent
  | MovieKeywordsUpdatedEvent
  | PersonCreatedEvent
  | PersonImagesUpdatedEvent
  | ImageDownloadedEvent
  | ImageDeletedEvent
  | GenreCreatedEvent
  | KeywordCreatedEvent;

export interface Image {
  type: "backdrop";
  sizes: {
    height: number;
    url: string;
    width: number;
  }[];
}

export interface Movie {
  id: string;
  images: Image[];
  genreIds: Genre["id"][];
  keywordIds: Keyword["id"][];
}

export interface Person {
  id: string;
  images: Image[];
}

export interface Genre {
  id: string;
  name: string;
}

export interface Keyword {
  id: string;
  name: string;
}

export interface CreateMovieArgs {
  id: string;
}

export function createMovie(
  eventBus: EventBus<Event>,
  create: CreateMovieArgs,
): Movie {
  const movie: Movie = {
    id: create.id,
    images: [],
    genreIds: [],
    keywordIds: [],
  };
  const event: MovieCreatedEvent = {
    payload: {
      id: movie.id,
    },
    type: "MovieCreatedEvent",
  };

  eventBus.push(event);

  return movie;
}

export interface UpdateMovieArgs {
  id: string;
  images: {
    type: "backdrop";
    sizes: {
      height: number;
      url: string;
      width: number;
    }[];
  }[];
  genreIds: string[];
  keywordIds: string[];
}

export function updateMovie(
  eventBus: EventBus<Event>,
  movie: Movie,
  update: UpdateMovieArgs,
): Movie {
  const updated: Movie = {
    ...movie,
    images: update.images,
    genreIds: update.genreIds,
    keywordIds: update.keywordIds,
  };

  if (!isEqual(movie.images, updated.images)) {
    const event: MovieImagesUpdatedEvent = {
      payload: {
        id: updated.id,
      },
      type: "MovieImagesUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (!isEqual(movie.genreIds, updated.genreIds)) {
    const event: MovieGenresUpdatedEvent = {
      payload: {
        id: updated.id,
      },
      type: "MovieGenresUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (!isEqual(movie.keywordIds, updated.keywordIds)) {
    const event: MovieKeywordsUpdatedEvent = {
      payload: {
        id: updated.id,
      },
      type: "MovieKeywordsUpdatedEvent",
    };

    eventBus.push(event);
  }

  return updated;
}

export interface CreatePersonArgs {
  id: string;
}

export function createPerson(
  eventBus: EventBus<Event>,
  create: CreatePersonArgs,
): Person {
  const person: Person = {
    id: create.id,
    images: [],
  };
  const event: PersonCreatedEvent = {
    payload: {
      id: person.id,
    },
    type: "PersonCreatedEvent",
  };

  eventBus.push(event);

  return person;
}

export interface UpdatePersonArgs {
  id: string;
  images: {
    type: "backdrop";
    sizes: {
      height: number;
      url: string;
      width: number;
    }[];
  }[];
}

export function updatePerson(
  eventBus: EventBus<Event>,
  person: Person,
  update: UpdatePersonArgs,
): Person {
  const updated: Person = {
    ...person,
    images: update.images,
  };

  if (!isEqual(person.images, updated.images)) {
    const event: PersonImagesUpdatedEvent = {
      payload: {
        id: updated.id,
      },
      type: "PersonImagesUpdatedEvent",
    };

    eventBus.push(event);
  }

  return updated;
}

export interface CreateGenreArgs {
  id: string;
  name: string;
}

export function createGenre(
  eventBus: EventBus<Event>,
  create: CreateGenreArgs,
): Genre {
  const genre: Genre = {
    id: create.id,
    name: create.name,
  };
  const event: GenreCreatedEvent = {
    payload: {
      id: genre.id,
      name: genre.name,
    },
    type: "GenreCreatedEvent",
  };

  eventBus.push(event);

  return genre;
}

export interface CreateKeywordArgs {
  id: string;
  name: string;
}

export function createKeyword(
  eventBus: EventBus<Event>,
  create: CreateKeywordArgs,
): Keyword {
  const keyword: Keyword = {
    id: create.id,
    name: create.name,
  };
  const event: KeywordCreatedEvent = {
    payload: {
      id: keyword.id,
      name: keyword.name,
    },
    type: "KeywordCreatedEvent",
  };

  eventBus.push(event);

  return keyword;
}

export interface GenreRepository {
  findMany(ids: string[]): Promise<(Genre | null)[]>;
  setMany(genres: Genre[]): Promise<void>;
}

export interface PersonRepository {
  findAll(): Promise<Person[]>;
  findMany(tmdbIds: string[]): Promise<(Person | null)[]>;
  setMany(people: Person[]): Promise<void>;
}

export interface KeywordRepository {
  findMany(ids: string[]): Promise<(Keyword | null)[]>;
  setMany(keywords: Keyword[]): Promise<void>;
}

export interface MovieRepository {
  findAll(): Promise<Movie[]>;
  findMany(tmdbIds: string[]): Promise<(Movie | null)[]>;
  setMany(movies: Movie[]): Promise<void>;
}

export interface ImageStorage {
  findMany(sourceUrls: string[]): Promise<(StorageImage | null)[]>;
  findBroken(): Promise<StorageImage[]>;
  deleteMany(images: StorageImage[]): Promise<void>;
  upload(stream: Readable, input: DownloadInput): Promise<StorageImage>;
}

export interface DownloadInput {
  height: number;
  width: number;
  sourceUrl: string;
}

export interface StorageImage {
  height: number;
  sourceUrl: string;
  url: string;
  width: number;
}

export interface JobQueue {
  getMovieJobQueue(): Promise<string[]>;
  getPeopleJobQueue(): Promise<string[]>;
}

export interface TmdbGateway {
  fetchMovie(tmdbId: string): Promise<TmdbGatewayFetchMovieResponse | null>;
  fetchPerson(tmdbId: string): Promise<TmdbGatewayFetchPersonResponse | null>;
  downloadAsManyImagesAsPossible(
    inputs: DownloadAsManyImagesAsPossibleInput[],
    callback: (
      stream: Readable,
      image: DownloadAsManyImagesAsPossibleInput,
    ) => Promise<void>,
  ): Promise<void>;
}

export interface TmdbGatewayFetchMovieResponse {
  images: {
    type: "backdrop";
    sizes: {
      height: number;
      url: string;
      width: number;
    }[];
  }[];
  genres: {
    id: string;
    name: string;
  }[];
  keywords: {
    id: string;
    name: string;
  }[];
}

export interface TmdbGatewayFetchPersonResponse {
  images: {
    type: "backdrop";
    sizes: {
      height: number;
      url: string;
      width: number;
    }[];
  }[];
}

export interface DownloadAsManyImagesAsPossibleInput {
  height: number;
  width: number;
  sourceUrl: string;
}
export class EventBusLogger implements EventBus<Event> {
  constructor(private wstream: Writable) {}

  push(event: Event): void {
    if (event.type === "MovieCreatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] The movie has been created (id: ${
          event.payload.id
        })\n`,
      );
    } else if (event.type === "MovieImagesUpdatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] The movie images has been updated (id: ${
          event.payload.id
        })\n`,
      );
    } else if (event.type === "MovieGenresUpdatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] The movie genres has been updated (id: ${
          event.payload.id
        })\n`,
      );
    } else if (event.type === "MovieKeywordsUpdatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] The movie keywords has been updated (id: ${
          event.payload.id
        })\n`,
      );
    } else if (event.type === "PersonCreatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] The person has been created (id: ${
          event.payload.id
        })\n`,
      );
    } else if (event.type === "PersonImagesUpdatedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] The person images has been updated (id: ${
          event.payload.id
        })\n`,
      );
    } else if (event.type === "GenreCreatedEvent") {
      this.wstream.write(
        `[${new Date().toTimeString().slice(0, 8)}] The genre "${
          event.payload.name
        }" has been created (id: ${event.payload.id})\n`,
      );
    } else if (event.type === "KeywordCreatedEvent") {
      this.wstream.write(
        `[${new Date().toTimeString().slice(0, 8)}] The keyword "${
          event.payload.name
        }" has been created (id: ${event.payload.id})\n`,
      );
    } else if (event.type === "ImageDeletedEvent") {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] The image has been deleted (sourceUrl: ${
          event.payload.sourceUrl
        }, url: ${event.payload.url})\n`,
      );
    } else {
      this.wstream.write(
        `[${new Date()
          .toTimeString()
          .slice(0, 8)}] The image has been downloaded (sourceUrl: ${
          event.payload.sourceUrl
        }, url: ${event.payload.url})\n`,
      );
    }
  }
}
